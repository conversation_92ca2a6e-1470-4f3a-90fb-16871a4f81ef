/* Math Editor Dialog Styles - Microsoft Word Style */
.matheditor-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: "Segoe UI", -apple-system, BlinkMacSystemFont, sans-serif;
}

.matheditor-content {
    background-color: #ffffff;
    border-radius: 2px;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15), 0 0 1px rgba(0, 0, 0, 0.1);
    width: 75%;
    max-width: 850px;
    max-height: 90vh;
    overflow: hidden;
    padding: 0;
    position: relative;
    border: 1px solid #d1d1d1;
}

.matheditor-content:focus-within {
    box-shadow: 0 6px 20px rgba(0, 120, 212, 0.15),
        0 0 1px rgba(0, 120, 212, 0.3);
    border-color: #0078d4;
}

.matheditor-container {
    padding: 0 16px;
    margin-top: 8px;
    overflow-y: auto;
    max-height: calc(90vh - 140px);
}

.matheditor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0;
    border-bottom: 1px solid #d1d1d1;
    padding: 0 16px;
    background-color: #ffffff;
    color: #323130;
    height: 36px;
    border-radius: 4px 4px 0 0;
    font-family: "Segoe UI", -apple-system, BlinkMacSystemFont, sans-serif;
}

.matheditor-header h2 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #323130;
}

.matheditor-close-btn {
    background: none;
    border: none;
    font-size: 16px;
    cursor: pointer;
    color: #605e5c;
    padding: 4px;
    line-height: 1;
    border-radius: 2px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.1s ease;
}

.matheditor-close-btn:hover {
    background-color: #f3f2f1;
    color: #323130;
}

.matheditor-input-container {
    margin-bottom: 16px;
    margin-top: 8px;
    padding: 0;
    font-family: "Segoe UI", -apple-system, BlinkMacSystemFont, sans-serif;
}

.matheditor-label {
    display: block;
    margin-bottom: 6px;
    font-weight: 600;
    color: #323130;
    font-size: 13px;
}

#matheditor-input {
    width: 99%;
    font-size: 16px;
    border: 1px solid #8a8886;
    border-radius: 2px;
    min-height: 160px;
    background-color: #ffffff;
    transition: all 0.1s ease;
    margin-bottom: 8px;
    padding: 8px;
}

#matheditor-input:focus {
    border-color: #0078d4;
    outline: none;
    box-shadow: 0 0 0 1px #0078d4;
}

/* Toolbar Tabs - Microsoft Word Style */
.matheditor-toolbar-tabs {
    display: flex;
    flex-wrap: nowrap;
    margin-bottom: 0;
    border-bottom: 1px solid #d1d1d1;
    padding: 0;
    background: #ffffff;
    overflow-x: auto;
    overflow-y: hidden;
    height: 32px;
    position: relative;
    z-index: 1;
    font-family: "Segoe UI", -apple-system, BlinkMacSystemFont, sans-serif;
}

.matheditor-toolbar-tabs::-webkit-scrollbar {
    height: 2px;
}

.matheditor-toolbar-tabs::-webkit-scrollbar-thumb {
    background-color: #c7c7c7;
    border-radius: 2px;
}

.matheditor-toolbar-tabs::-webkit-scrollbar-track {
    background-color: transparent;
}

.matheditor-tab-btn {
    padding: 0 12px;
    border: none;
    background-color: transparent;
    cursor: pointer;
    font-size: 13px;
    font-weight: 400;
    transition: all 0.15s ease;
    margin: 0;
    color: #323130;
    position: relative;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    border-radius: 0;
    min-width: 60px;
    white-space: nowrap;
}

.matheditor-tab-btn:hover {
    color: #106ebe;
    background-color: #f3f2f1;
}

.matheditor-tab-btn:after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #0078d4;
    transition: all 0.15s ease;
    transform: scaleX(0);
    transform-origin: center;
}

.matheditor-tab-btn:hover:after {
    transform: scaleX(0.3);
    opacity: 0.6;
}

.matheditor-tab-btn.active {
    background-color: #ffffff;
    color: #0078d4;
    font-weight: 600;
    z-index: 2;
}

.matheditor-tab-btn.active:after {
    transform: scaleX(1);
    opacity: 1;
}

/* Toolbar Content - Microsoft Word Style */
.matheditor-toolbar {
    display: block;
    margin-bottom: 16px;
    padding: 8px 12px;
    background-color: #faf9f8;
    border: none;
    border-radius: 0;
    height: 72px;
    overflow-y: hidden;
    overflow-x: auto;
    border-bottom: 1px solid #edebe9;
    position: relative;
    z-index: 1;
    font-family: "Segoe UI", -apple-system, BlinkMacSystemFont, sans-serif;
}

.matheditor-tab-content {
    display: none;
    flex-wrap: nowrap;
    justify-content: flex-start;
    align-items: center;
    height: 100%;
    overflow: hidden;
    gap: 4px;
}

.matheditor-tab-content.active {
    display: flex;
}

.matheditor-toolbar-btn {
    padding: 4px 6px;
    border: 1px solid transparent;
    border-radius: 2px;
    background-color: transparent;
    cursor: pointer;
    font-size: 12px;
    font-weight: 400;
    transition: all 0.1s ease;
    min-width: 28px;
    height: 28px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 1px;
    color: #323130;
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
    font-family: "Segoe UI", -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Style for FontAwesome icons in buttons */
.matheditor-toolbar-btn i {
    font-size: 12px;
    color: #323130;
    transition: all 0.1s ease;
}

/* Style for text next to icons */
.matheditor-toolbar-btn .btn-text {
    margin-left: 3px;
    font-size: 11px;
    font-weight: 400;
}

.matheditor-toolbar-btn:hover {
    background-color: #f3f2f1;
    color: #323130;
    border-color: #c7c7c7;
}

.matheditor-toolbar-btn:hover i {
    color: #323130;
}

.matheditor-toolbar-btn:active {
    background-color: #edebe9;
    border-color: #8a8886;
    color: #323130;
}

/* Large button variant for important functions */
.matheditor-toolbar-btn.large {
    padding: 6px 8px;
    font-size: 13px;
    min-width: 32px;
    height: 32px;
    font-weight: 500;
    border-radius: 3px;
}

.matheditor-toolbar-btn.large i {
    font-size: 14px;
}

/* Button group separators */
.matheditor-toolbar-btn + .matheditor-toolbar-btn {
    margin-left: 1px;
}

.matheditor-toolbar-btn.group-separator {
    margin-left: 8px;
    position: relative;
}

.matheditor-toolbar-btn.group-separator::before {
    content: "";
    position: absolute;
    left: -5px;
    top: 2px;
    bottom: 2px;
    width: 1px;
    background-color: #d2d0ce;
}

/* No longer using category headers */

.matheditor-buttons-container {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 16px;
    padding: 12px 0 0;
    border-top: 1px solid #edebe9;
    font-family: "Segoe UI", -apple-system, BlinkMacSystemFont, sans-serif;
}

.matheditor-cancel-btn {
    padding: 6px 16px;
    border: 1px solid #8a8886;
    border-radius: 2px;
    background-color: #ffffff;
    cursor: pointer;
    font-size: 13px;
    color: #323130;
    font-weight: 400;
    transition: all 0.1s ease;
    position: relative;
    overflow: hidden;
    min-height: 28px;
}

.matheditor-cancel-btn:hover {
    background-color: #f3f2f1;
    border-color: #605e5c;
    color: #323130;
}

.matheditor-cancel-btn:active {
    background-color: #edebe9;
    border-color: #323130;
}

.matheditor-insert-btn {
    padding: 6px 16px;
    border: 1px solid #0078d4;
    border-radius: 2px;
    background-color: #0078d4;
    color: #ffffff;
    cursor: pointer;
    font-size: 13px;
    font-weight: 400;
    transition: all 0.1s ease;
    position: relative;
    overflow: hidden;
    min-height: 28px;
}

.matheditor-insert-btn:hover {
    background-color: #106ebe;
    border-color: #106ebe;
}

.matheditor-insert-btn:active {
    background-color: #005a9e;
    border-color: #005a9e;
}

/* Hide MathLive virtual keyboard toggle button */
.ML__virtual-keyboard-toggle,
[part="virtual-keyboard-toggle"],
[data-ml__tooltip="Toggle Virtual Keyboard"],
[data-command="&quot;toggleVirtualKeyboard&quot;"] {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
    width: 0 !important;
    height: 0 !important;
    position: absolute !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
}

math-field::part(virtual-keyboard-toggle) {
    display: none !important;
}
