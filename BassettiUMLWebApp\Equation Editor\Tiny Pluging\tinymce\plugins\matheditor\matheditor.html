<!-- Math Editor Dialog Template -->
<div class="matheditor-dialog">
    <div class="matheditor-content">
        <div class="matheditor-header">
            <h2>Math Editor</h2>
            <button class="matheditor-close-btn" title="Close">&times;</button>
        </div>

        <!-- Container for all content except navbar -->
        <div class="matheditor-container">
            <!-- Toolbar Tabs - Moved to top -->
            <div class="matheditor-toolbar-tabs">
                <button class="matheditor-tab-btn active" data-tab="basic">
                    Basic
                </button>
                <button class="matheditor-tab-btn" data-tab="operators">
                    Operators
                </button>
                <button class="matheditor-tab-btn" data-tab="relations">
                    Relations
                </button>
                <button class="matheditor-tab-btn" data-tab="greek">
                    Greek Letters
                </button>
                <button class="matheditor-tab-btn" data-tab="arrows">
                    Arrows
                </button>
                <button class="matheditor-tab-btn" data-tab="functions">
                    Functions
                </button>
                <button class="matheditor-tab-btn" data-tab="matrices">
                    Matrices
                </button>
            </div>

            <!-- Toolbar Content - Moved to top -->
            <div class="matheditor-toolbar">
                <!-- Basic Tab Content (visible by default) -->
                <div class="matheditor-tab-content active" id="basic-tab">
                    <!-- Basic symbols and labels will be inserted here dynamically -->
                </div>

                <!-- Operators Tab Content -->
                <div class="matheditor-tab-content" id="operators-tab">
                    <!-- Operator symbols and labels will be inserted here dynamically -->
                </div>

                <!-- Relations Tab Content -->
                <div class="matheditor-tab-content" id="relations-tab">
                    <!-- Relation symbols and labels will be inserted here dynamically -->
                </div>

                <!-- Greek Letters Tab Content -->
                <div class="matheditor-tab-content" id="greek-tab">
                    <!-- Greek letters and labels will be inserted here dynamically -->
                </div>

                <!-- Arrows Tab Content -->
                <div class="matheditor-tab-content" id="arrows-tab">
                    <!-- Arrow symbols and labels will be inserted here dynamically -->
                </div>

                <!-- Functions Tab Content -->
                <div class="matheditor-tab-content" id="functions-tab">
                    <!-- Function symbols and labels will be inserted here dynamically -->
                </div>

                <!-- Matrices Tab Content -->
                <div class="matheditor-tab-content" id="matrices-tab">
                    <!-- Matrix templates and labels will be inserted here dynamically -->
                </div>
            </div>

            <div class="matheditor-input-container">
                <label class="matheditor-label">Edit Equation:</label>
                <math-field id="matheditor-input"></math-field>
            </div>

            <div class="matheditor-buttons-container">
                <button class="matheditor-cancel-btn">Cancel</button>
                <button class="matheditor-insert-btn">Insert</button>
            </div>
        </div>
    </div>
</div>
